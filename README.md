# 水分传感器Modbus通讯工具

这是一个用于读取温湿度水分传感器数据的Python工具，支持多种Modbus通讯方式。

## 功能特性

- 🌡️ **多传感器支持**: 可同时读取多个温湿度水分传感器数据
- 🔌 **多种通讯方式**: 支持串口、TCP/IP和分机通讯
- ⚡ **异步操作**: 基于asyncio的高性能异步通讯
- 🔍 **详细调试**: 支持协议级别的详细调试输出
- 📊 **数据解析**: 自动解析传感器数据（温度/湿度/水分）

## 支持的通讯方式

### 1. 串口通讯 (Modbus RTU)
通过RS485串口直接连接传感器

### 2. TCP/IP通讯 (Modbus TCP)
通过网络连接Modbus TCP设备

### 3. 分机通讯 (Extension)
通过TCP/IP转RS485分机设备进行通讯，支持自定义加密

## 安装方法

### 方法1: 使用pip安装依赖
```bash
# 安装最小依赖
pip install -r requirements-minimal.txt

# 或安装完整依赖（固定版本）
pip install -r requirements.txt

# 开发环境安装
pip install -r requirements-dev.txt
```

### 方法2: 使用setup.py安装
```bash
# 安装项目
pip install -e .

# 安装开发依赖
pip install -e .[dev]
```

## 使用方法

### 基本用法
```bash
# 读取1个传感器数据（串口）
python sensor_reader.py --serial /dev/ttyUSB0 --count 1

# 读取多个传感器数据（TCP/IP）
python sensor_reader.py --ip ************* --count 4

# 通过分机读取数据
python sensor_reader.py --extension ************* --count 2
```

### 详细调试
```bash
# 显示详细的协议通讯内容
python sensor_reader.py --serial /dev/ttyUSB0 --verbose
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--count` | 传感器数量 | 1 |
| `--address` | Modbus从站地址 | 1 |
| `--serial` | 串口设备路径 | - |
| `--baudrate` | 波特率 | 9600 |
| `--ip` | TCP/IP地址 | - |
| `--port` | TCP端口 | 502 |
| `--extension` | 分机IP地址 | - |
| `--extension-port` | 分机端口 | 3456 |
| `--verbose` | 详细调试输出 | False |

## 输出格式

传感器数据以 `温度/湿度/水分` 的格式输出：
```
传感器 1: 25.3/65.2/12.45
传感器 2: 24.8/63.7/11.89
```

## 项目结构

```
├── sensor_reader.py          # 主程序
├── extension_client.py       # 分机通讯客户端
├── mock_extension_server.py  # 模拟分机服务器（测试用）
├── myio.py                  # 通讯工具函数
├── requirements.txt         # 完整依赖列表
├── requirements-minimal.txt # 最小依赖列表
├── requirements-dev.txt     # 开发依赖列表
├── setup.py                # 安装脚本
├── pyproject.toml          # 现代Python项目配置
└── README.md               # 本文件
```

## 开发和测试

### 运行测试
```bash
# 测试分机通讯
python test_extension_client.py

# 测试完整通讯流程
python test_extension_communication.py
```

### 代码格式化
```bash
# 使用black格式化代码
black *.py

# 使用isort整理导入
isort *.py
```

## 技术细节

- **Python版本**: 3.8+
- **核心依赖**: pymodbus, pyserial, aiohttp
- **通讯协议**: Modbus RTU/TCP
- **异步框架**: asyncio

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
