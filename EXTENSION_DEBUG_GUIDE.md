# 分机通讯调试功能使用指南

## 概述

为分机通讯功能添加了详细的调试输出，可以显示加密前后的数据以及完整的通讯过程。这些调试信息只有在使用 `--verbose` 选项时才会输出。

## 功能特性

### 1. 调试输出控制
- 只有在使用 `--verbose` 或 `-v` 选项时才显示调试信息
- 不使用 `--verbose` 时，程序正常运行，不产生额外输出
- 调试信息带有精确的时间戳（精确到毫秒）

### 2. 详细的数据跟踪

#### 发送数据流程
1. **原始Modbus数据**：显示要发送的原始Modbus RTU数据
2. **rs485命令(加密前)**：显示构建的rs485命令字符串
3. **rs485命令字节(加密前)**：显示命令字符串的字节表示
4. **加密后数据**：显示实际传输给分机的加密数据

#### 接收数据流程
1. **接收加密数据**：显示从分机接收到的加密响应
2. **解密后字节**：显示解密后的原始字节数据
3. **解密后字符串**：显示解密后的字符串内容
4. **解析后Modbus响应**：显示最终解析的Modbus响应数据

## 使用方法

### 基本用法
```bash
# 不启用调试输出（正常模式）
python3 sensor_reader.py --sub 192.168.1.100 --count 2

# 启用调试输出（详细模式）
python3 sensor_reader.py --sub 192.168.1.100 --count 2 --verbose
```

### 输出示例

#### 正常模式输出
```
2025-07-30 10:03:39 25.0/39.9/10.38 25.1/38.3/10.20
```

#### 详细模式输出
```
=== 启用详细通讯日志 ===
正在连接到 127.0.0.1 (分机模式)...
连接成功，正在读取数据...
[10:03:46.652] [发送] PDU: 功能码=3, 从站=1
[10:03:46.652] [发送] 字节数据: 01 03 27 10 00 08 4F 7D
[10:03:46.652] [发送] 数据长度: 8 字节
[10:03:46.652] [分机调试] 原始Modbus数据: 01 03 27 10 00 08 4F 7D (8 字节)
[10:03:46.653] [分机调试] rs485命令(加密前): rs485 -p dev/ttyS2 -b 9600 -t 1000 01 03 27 10 00 08 4F 7D
[10:03:46.653] [分机调试] rs485命令字节(加密前): 72 73 34 38 35 20 2D 70 ... (58 字节)
[10:03:46.653] [分机调试] 加密后数据: BB C8 DF B9 CB DF C7 CF ... (58 字节)
[10:03:46.654] [分机调试] 接收加密数据: C6 C8 DF CD BB DF BA BA ... (110 字节)
[10:03:46.654] [分机调试] 解密后字节: 30 31 20 30 33 20 32 30 ... (110 字节)
[10:03:46.654] [分机调试] 解密后字符串: 01 03 20 01 01 00 FA 01 8F 04 0E ...
[10:03:46.654] [分机调试] 解析后Modbus响应: 01 03 20 01 01 00 FA 01 8F ... (37 字节)
[10:03:46.654] [接收] 字节数据: 01 03 20 01 01 00 FA 01 8F 04 0E ...
[10:03:46.654] [接收] 数据长度: 37 字节
[10:03:46.654] [接收] PDU: 功能码=3, 从站=1
2025-07-30 10:03:46 25.0/39.9/10.38 25.1/38.3/10.20
```

## 调试信息说明

### 时间戳格式
- 格式：`[HH:MM:SS.mmm]`
- 精确到毫秒，便于分析通讯时序

### 数据格式
- 所有字节数据以十六进制显示，用空格分隔
- 显示数据长度，便于验证数据完整性
- 区分不同阶段的数据（原始、加密、解密、解析）

### 标识符说明
- `[分机调试]`：分机特有的调试信息
- `[发送]`：pymodbus框架的发送跟踪
- `[接收]`：pymodbus框架的接收跟踪

## 应用场景

### 1. 通讯问题诊断
- 检查数据是否正确加密/解密
- 验证rs485命令格式是否正确
- 分析通讯失败的具体环节

### 2. 协议分析
- 了解分机加密算法的实际效果
- 观察Modbus数据的传输过程
- 验证数据完整性

### 3. 性能优化
- 分析通讯延迟
- 观察数据传输效率
- 检测重传情况

### 4. 开发调试
- 验证新功能的正确性
- 测试不同参数的效果
- 排查代码问题

## 注意事项

1. **性能影响**：启用详细调试会产生大量输出，可能略微影响性能
2. **日志大小**：长时间运行会产生大量日志数据
3. **安全考虑**：调试信息可能包含敏感数据，生产环境中谨慎使用
4. **存储空间**：如果重定向到文件，注意磁盘空间使用

## 技术实现

### 核心功能
- 在 `AsyncModbusExtensionClient` 类中添加 `verbose` 参数
- 实现 `_debug_print()` 方法控制调试输出
- 在 `_send_receive_raw()` 方法中添加详细的数据跟踪

### 兼容性
- 完全向后兼容，不影响现有功能
- 默认不启用调试输出，保持原有行为
- 与pymodbus的trace功能协同工作

## 总结

新增的调试功能为分机通讯提供了全面的数据可视化，大大提高了问题诊断和协议分析的效率。通过 `--verbose` 选项的简单控制，用户可以在需要时获得详细的通讯信息，在正常使用时保持简洁的输出。
