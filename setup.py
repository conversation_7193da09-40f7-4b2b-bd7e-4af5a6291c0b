#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
水分传感器Modbus通讯项目安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_file(filename):
    with open(os.path.join(os.path.dirname(__file__), filename), encoding='utf-8') as f:
        return f.read()

# 读取requirements文件
def read_requirements(filename):
    requirements = []
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('-r'):
                requirements.append(line)
    return requirements

setup(
    name="water-sensor-modbus",
    version="1.0.0",
    description="温湿度水分传感器Modbus通讯工具",
    long_description="支持通过串口、TCP/IP和分机方式进行Modbus通讯的温湿度水分传感器数据读取工具",
    long_description_content_type="text/plain",
    author="Water Sensor Team",
    author_email="",
    url="",
    
    # 包配置
    packages=find_packages(),
    py_modules=[
        'sensor_reader',
        'extension_client', 
        'mock_extension_server',
        'myio'
    ],
    
    # 依赖配置
    install_requires=read_requirements('requirements-minimal.txt'),
    extras_require={
        'dev': read_requirements('requirements-dev.txt')[1:],  # 跳过-r requirements.txt行
        'full': read_requirements('requirements.txt'),
    },
    
    # Python版本要求
    python_requires='>=3.8',
    
    # 分类信息
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'Topic :: System :: Hardware',
        'Topic :: Communications',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Programming Language :: Python :: 3.12',
    ],
    
    # 关键词
    keywords='modbus sensor water temperature humidity serial tcp extension',
    
    # 入口点在pyproject.toml中定义
    
    # 包含的数据文件
    include_package_data=True,
    package_data={
        '': ['*.md', '*.txt', '*.pdf'],
    },
    
    # 项目URL
    project_urls={
        'Documentation': '',
        'Source': '',
        'Tracker': '',
    },
)
