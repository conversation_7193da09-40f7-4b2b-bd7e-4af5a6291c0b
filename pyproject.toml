[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "water-sensor-modbus"
version = "1.0.0"
description = "温湿度水分传感器Modbus通讯工具"
readme = "REQUIREMENTS_GUIDE.md"
license = {text = "MIT"}
authors = [
    {name = "Water Sensor Team"}
]
keywords = ["modbus", "sensor", "water", "temperature", "humidity", "serial", "tcp", "extension"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Topic :: System :: Hardware",
    "Topic :: Communications",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.8"
dependencies = [
    "pymodbus>=3.9.0",
    "pyserial>=3.5",
    "aiohttp>=3.12.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "flake8>=6.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "sphinx>=6.0.0",
    "ipdb>=0.13.0",
]

[project.scripts]
sensor-reader = "sensor_reader:main"

[tool.setuptools]
py-modules = ["sensor_reader", "extension_client", "mock_extension_server", "myio"]

[project.entry-points]

[tool.setuptools.package-data]
"*" = ["*.md", "*.txt", "*.pdf"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | pyroot
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q"
testpaths = [
    "tests",
]
asyncio_mode = "auto"
