# 项目依赖安装指南

## 依赖文件说明

本项目提供了三个不同的requirements文件：

### 1. `requirements.txt` - 完整依赖列表
包含所有当前安装的包及其精确版本号，适用于：
- 生产环境部署
- 确保环境一致性
- 复现特定版本的运行环境

### 2. `requirements-minimal.txt` - 最小依赖
只包含项目直接依赖的核心包，适用于：
- 新环境快速安装
- 允许依赖包自动更新到兼容版本
- 减少依赖冲突

### 3. `requirements-dev.txt` - 开发环境依赖
包含开发和测试所需的额外工具，适用于：
- 开发环境搭建
- 代码质量检查
- 单元测试运行

## 安装方法

### 生产环境安装
```bash
pip install -r requirements.txt
```

### 最小依赖安装
```bash
pip install -r requirements-minimal.txt
```

### 开发环境安装
```bash
pip install -r requirements-dev.txt
```

## 虚拟环境推荐

建议使用虚拟环境来隔离项目依赖：

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 退出虚拟环境
deactivate
```

## 核心依赖说明

- **pymodbus**: Modbus协议通讯库，支持串口、TCP/IP和RTU通讯
- **pyserial**: 串口通讯支持
- **aiohttp**: 异步HTTP客户端，用于分机通讯功能

## 更新依赖

如果需要更新项目依赖：

```bash
# 更新所有包到最新版本
pip install --upgrade -r requirements.txt

# 生成新的requirements文件
pip freeze > requirements.txt
```
